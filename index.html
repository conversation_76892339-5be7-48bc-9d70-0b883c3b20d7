<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title><PERSON>'s Social Media Pipeline</title>
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@500;700&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: 'Montserrat', sans-serif;
      background: linear-gradient(to right, #fceabb, #f8b500);
      color: #2c2c2c;
      margin: 0;
      padding: 2rem;
      animation: fadeIn 1.5s ease-in;
    }
    h1 {
      text-align: center;
      font-size: 2.5rem;
      margin-bottom: 1rem;
    }
    .subtitle {
      text-align: center;
      font-size: 1.2rem;
      margin-bottom: 2rem;
      color: #444;
    }
    .stage {
      display: flex;
      align-items: flex-start;
      margin: 1.5rem 0;
      padding: 1rem;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      transition: transform 0.3s ease;
    }
    .stage:hover {
      transform: scale(1.02);
    }
    .icon {
      font-size: 2rem;
      margin-right: 1rem;
      margin-top: 0.5rem;
    }
    .content h2 {
      margin: 0;
      font-size: 1.5rem;
      color: #f8b500;
    }
    .content p {
      margin: 0.5rem 0 0;
      font-size: 1rem;
    }
    .example {
      font-style: italic;
      color: #555;
      margin-top: 0.3rem;
    }
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
  </style>
</head>
<body>
  <h1>Beyoncé’s Social Media Mastery</h1>
  <div class="subtitle">How Big Data Analytics Reveals Her Cultural Impact</div>

  <div class="stage">
    <div class="icon">📥</div>
    <div class="content">
      <h2>1. Data Acquisition</h2>
      <p>Gather posts, likes, shares, and comments from Instagram, Twitter, Facebook, and YouTube.</p>
      <p class="example">Example: Pulling Beyoncé’s Instagram posts during the “Lemonade” release weekend.</p>
    </div>
  </div>

  <div class="stage">
    <div class="icon">🧹</div>
    <div class="content">
      <h2>2. Cleaning & Extraction</h2>
      <p>Remove spam, extract hashtags, mentions, emojis, and sentiment from captions and replies.</p>
      <p class="example">Example: Identifying positive vs. negative reactions to a surprise album drop.</p>
    </div>
  </div>

  <div class="stage">
    <div class="icon">🔗</div>
    <div class="content">
      <h2>3. Integration & Representation</h2>
      <p>Merge data across platforms, normalize formats, and represent user interactions in structured models.</p>
      <p class="example">Example: Linking Twitter retweets with Instagram likes to track cross-platform buzz.</p>
    </div>
  </div>

  <div class="stage">
    <div class="icon">📊</div>
    <div class="content">
      <h2>4. Modeling & Analysis</h2>
      <p>Analyze engagement spikes, rumor spread, and influencer amplification using NLP and time-series models.</p>
      <p class="example">Example: Measuring how one cryptic post fuels both speculation and brand loyalty.</p>
    </div>
  </div>

  <div class="stage">
    <div class="icon">🧠</div>
    <div class="content">
      <h2>5. Interpretation</h2>
      <p>Visualize Beyoncé’s influence, narrative control, and cultural resonance through dashboards and reports.</p>
      <p class="example">Example: A dashboard showing how Beyoncé’s posts drive global conversation within minutes.</p>
    </div>
  </div>
</body>
</html>