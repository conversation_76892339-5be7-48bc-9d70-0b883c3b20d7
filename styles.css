/* Base styling */
body {
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f7fa;
  color: #333;
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

header {
  background-color: #2c3e50;
  color: white;
  text-align: center;
  padding: 3rem 1rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

h1 {
  font-size: 3.5rem;
  margin: 0;
  font-weight: 900;
  letter-spacing: 2px;
}

.menu-section {
  padding: 3rem 2rem;
  margin: 2rem;
  background: white;
  border-radius: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
}

.menu-section h2 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 3px solid #3498db;
  font-weight: 800;
  letter-spacing: 1px;
}

/* Grid layout for main dishes */
.menu-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2.5rem;
}

.menu-item {
  background: white;
  border: 1px solid #e1e8ed;
  border-radius: 15px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.menu-item img {
  width: 100%;
  height: 500px;
  border-radius: 10px;
  object-fit: cover;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.menu-item h3 {
  font-size: 1.5rem;
  margin: 1rem 0;
  color: #2c3e50;
  font-weight: 700;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.menu-item h3 span {
  color: #e74c3c;
  font-weight: 800;
  font-size: 1.3rem;
}

.menu-item p {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin-top: 1rem;
}

/* List styling for beverages and desserts */
.beverage-list,
.dessert-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.beverage-list li,
.dessert-list li {
  font-size: 1rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e0c4b2;
  display: flex;
  justify-content: space-between;
  color: #5c2e1f;
}

.beverage-list li span,
.dessert-list li span {
  color: #8c4c2f;
  font-weight: bold;
}

h2 {
    text-align: center;
}

