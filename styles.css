/* Base styling */
body {
  font-family: 'Georgia', 'Times New Roman', serif;
  background-color: #f8f8f8;
  color: #333;
  margin: 0;
  padding: 2rem;
  max-width: 420mm;
  min-height: 594mm;
}

/* Print styles for A2 paper */
@media print {
  body {
    background-color: white;
    max-width: 420mm;
    max-height: 594mm;
    margin: 0;
    padding: 0;
  }

  @page {
    size: A2;
    margin: 10mm;
  }
}

header {
  background-color: transparent;
  color: #333;
  text-align: center;
  padding: 2rem 0;
  position: relative;
}

header::before,
header::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 40%;
  height: 1px;
  background-color: #333;
}

header::before {
  left: 5%;
}

header::after {
  right: 5%;
}

/* Decorative diamond */
header::before {
  background: linear-gradient(to right, #333 0%, #333 45%, transparent 50%, #333 55%, #333 100%);
}

header::after {
  background: linear-gradient(to left, #333 0%, #333 45%, transparent 50%, #333 55%, #333 100%);
}

.restaurant-name {
  font-size: 0.9rem;
  letter-spacing: 3px;
  margin-bottom: 0.5rem;
  font-weight: normal;
  text-transform: uppercase;
}

h1 {
  font-size: 3.5rem;
  margin: 0;
  font-weight: bold;
  letter-spacing: 8px;
  text-transform: uppercase;
}

.menu-section {
  padding: 2rem 0;
  margin: 0;
  background: transparent;
  border: none;
  box-shadow: none;
}

.menu-section h2 {
  display: none;
}

/* Grid layout for main dishes */
.menu-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 3rem 4rem;
  margin-bottom: 3rem;
}

.menu-item {
  background: transparent;
  border: none;
  border-radius: 0;
  padding: 0;
  text-align: left;
  box-shadow: none;
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
}

.menu-item img {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
  order: 2;
}

.menu-content {
  flex: 1;
  order: 1;
}

.menu-item h3 {
  font-size: 1.4rem;
  margin: 0 0 0.5rem 0;
  color: #333;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  align-items: baseline;
}

.menu-item .price {
  color: #333;
  font-weight: bold;
  font-size: 1rem;
}

.menu-item p {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.5;
  margin: 0;
}

/* List styling for beverages and desserts */
.beverage-list,
.dessert-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.beverage-list li,
.dessert-list li {
  font-size: 1rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e0c4b2;
  display: flex;
  justify-content: space-between;
  color: #5c2e1f;
}

.beverage-list li span,
.dessert-list li span {
  color: #8c4c2f;
  font-weight: bold;
}

h2 {
    font-size: 1.8rem;
    margin: 3rem 0 2rem 0;
    color: #333;
    font-weight: bold;
    text-align: left;
}

/* Bottom sections styling */
.bottom-sections {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid #333;
  position: relative;
}

.bottom-sections::before {
  content: '♦';
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #f8f8f8;
  padding: 0 1rem;
  font-size: 0.8rem;
}

.beverage-section,
.dessert-section {
  padding: 0;
}

.beverage-section h2,
.dessert-section h2 {
  margin-bottom: 1.5rem;
}

.beverage-list,
.dessert-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.beverage-list li,
.dessert-list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 0;
  border-bottom: 1px dotted #ccc;
  font-size: 1rem;
  color: #333;
}

.beverage-list li:last-child,
.dessert-list li:last-child {
  border-bottom: none;
}

.beverage-list li span,
.dessert-list li span {
  font-weight: bold;
  color: #333;
}

