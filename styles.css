/* Base styling */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f7fa;
  color: #333;
  margin: 0;
  padding: 0;
  max-width: 420mm;
  min-height: 594mm;
}

/* Print styles for A2 paper */
@media print {
  body {
    background-color: white;
    max-width: 420mm;
    max-height: 594mm;
    margin: 0;
    padding: 0;
  }

  @page {
    size: A2;
    margin: 10mm;
  }
}

header {
  background-color: #2c3e50;
  color: white;
  text-align: center;
  padding: 1rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

h1 {
  font-size: 2rem;
  margin: 0;
  font-weight: 900;
  letter-spacing: 1px;
}

.menu-section {
  padding: 0.8rem 1rem;
  margin: 0.5rem;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
}

.menu-section h2 {
  font-size: 1.8rem;
  color: #2c3e50;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #3498db;
  font-weight: 800;
  letter-spacing: 1px;
}

/* Grid layout for main dishes */
.menu-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.menu-item {
  background: white;
  border: 1px solid #e1e8ed;
  border-radius: 10px;
  padding: 0.6rem;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.menu-item img {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  object-fit: cover;
  margin-bottom: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.menu-item h3 {
  font-size: 1.1rem;
  margin: 0.5rem 0;
  color: #2c3e50;
  font-weight: 700;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.menu-item h3 span {
  color: #e74c3c;
  font-weight: 800;
  font-size: 1rem;
}

.menu-item p {
  font-size: 0.85rem;
  color: #666;
  line-height: 1.4;
  margin-top: 0.5rem;
}

/* List styling for beverages and desserts */
.beverage-list,
.dessert-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.beverage-list li,
.dessert-list li {
  font-size: 1rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e0c4b2;
  display: flex;
  justify-content: space-between;
  color: #5c2e1f;
}

.beverage-list li span,
.dessert-list li span {
  color: #8c4c2f;
  font-weight: bold;
}

h2 {
    text-align: center;
    font-size: 1.5rem;
    margin: 0.5rem 0;
    color: #2c3e50;
    font-weight: 800;
}

