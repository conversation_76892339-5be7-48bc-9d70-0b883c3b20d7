/* Base styling */
body {
  font-family: 'Georgia', 'Times New Roman', serif;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  color: #fff;
  margin: 0;
  padding: 1.5rem;
  max-width: 420mm;
  min-height: 594mm;
}

/* Print styles for A2 paper */
@media print {
  body {
    background-color: white;
    max-width: 420mm;
    max-height: 594mm;
    margin: 0;
    padding: 0;
  }

  @page {
    size: A2;
    margin: 10mm;
  }
}

header {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  color: #fff;
  text-align: center;
  padding: 3rem 2rem;
  position: relative;
  border-radius: 20px;
  margin-bottom: 2rem;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.restaurant-name {
  font-size: 1.4rem;
  letter-spacing: 4px;
  margin-bottom: 1rem;
  font-weight: bold;
  text-transform: uppercase;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

h1 {
  font-size: 4.5rem;
  margin: 0;
  font-weight: 900;
  letter-spacing: 10px;
  text-transform: uppercase;
  text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.4);
}

.menu-section {
  padding: 2rem;
  margin: 0;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 25px;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.4);
  border: 3px solid #ff6b35;
}

.menu-section h2 {
  display: none;
}

/* Grid layout for main dishes */
.menu-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2.5rem;
  margin-bottom: 0;
}

.menu-item {
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  border: 2px solid #ff6b35;
  border-radius: 20px;
  padding: 2rem;
  text-align: left;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 2.5rem;
}

.menu-item img {
  width: 200px;
  height: 200px;
  border-radius: 20px;
  object-fit: cover;
  flex-shrink: 0;
  order: 2;
  border: 4px solid #ff6b35;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.menu-content {
  flex: 1;
  order: 1;
}

.menu-item h3 {
  font-size: 2.2rem;
  margin: 0 0 1rem 0;
  color: #1a1a2e;
  font-weight: 900;
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.menu-item .price {
  color: #ff6b35;
  font-weight: 900;
  font-size: 2rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.menu-item p {
  font-size: 1.3rem;
  color: #444;
  line-height: 1.6;
  margin: 0;
  font-weight: 500;
}



