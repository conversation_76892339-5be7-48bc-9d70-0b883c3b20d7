/* Base styling */
body {
  font-family: 'Segoe UI', sans-serif;
  background-color: #fdfdfd;
  color: #333;
  margin: 0;
  padding: 0;
}

header {
  background-color: #8c4c2f;
  color: white;
  text-align: center;
  padding: 2rem 1rem;
}

h1 {
  font-size: 2.5rem;
  margin: 0;
}

.menu-section {
  padding: 2rem;
}

.menu-section h2 {
  font-size: 2rem;
  color: #8c4c2f;
  margin-bottom: 1rem;
  border-bottom: 2px solid #e0c4b2;
  padding-bottom: 0.5rem;
}

/* Grid layout for main dishes */
.menu-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.menu-item {
  background-color: #fff8f4;
  border: 1px solid #e0c4b2;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
}

.menu-item img {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 1rem;
}

.menu-item h3 {
  font-size: 1.2rem;
  margin: 0.5rem 0;
  color: #5c2e1f;
}

.menu-item h3 span {
  float: right;
  color: #8c4c2f;
  font-weight: bold;
}

.menu-item p {
  font-size: 0.95rem;
  color: #555;
}

/* List styling for beverages and desserts */
.beverage-list,
.dessert-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.beverage-list li,
.dessert-list li {
  font-size: 1rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e0c4b2;
  display: flex;
  justify-content: space-between;
  color: #5c2e1f;
}

.beverage-list li span,
.dessert-list li span {
  color: #8c4c2f;
  font-weight: bold;
}