/* Base styling */
body {
  font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

header {
  background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
  background-size: 400% 400%;
  animation: gradientShift 4s ease infinite;
  color: white;
  text-align: center;
  padding: 3rem 1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
}

header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 1;
}

h1 {
  font-size: 3.5rem;
  margin: 0;
  font-weight: 900;
  text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.5);
  position: relative;
  z-index: 2;
  letter-spacing: 2px;
  animation: pulse 2s ease-in-out infinite alternate;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes pulse {
  from { transform: scale(1); }
  to { transform: scale(1.05); }
}

.menu-section {
  padding: 3rem 2rem;
  margin: 2rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.menu-section h2 {
  font-size: 2.5rem;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 3px solid transparent;
  border-image: linear-gradient(45deg, #ff6b6b, #4ecdc4) 1;
  font-weight: 800;
  letter-spacing: 1px;
}

/* Grid layout for main dishes */
.menu-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2.5rem;
}

.menu-item {
  background: linear-gradient(145deg, #ffffff, #f0f0f0);
  border: none;
  border-radius: 20px;
  padding: 1.5rem;
  text-align: center;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.menu-item:hover::before {
  left: 100%;
}

.menu-item:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow:
    0 30px 60px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

.menu-item img {
  width: 100%;
  height: 450px;
  border-radius: 15px;
  object-fit: fill;
  margin-bottom: 1.5rem;
  box-shadow:
    0 15px 30px rgba(0, 0, 0, 0.2),
    0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  filter: brightness(1.1) contrast(1.1);
}

.menu-item:hover img {
  transform: scale(1.05);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 10px 20px rgba(0, 0, 0, 0.15);
}

.menu-item h3 {
  font-size: 1.5rem;
  margin: 1rem 0;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.menu-item h3 span {
  background: linear-gradient(45deg, #ff6b6b, #feca57);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
  font-size: 1.3rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.menu-item p {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin-top: 1rem;
}

/* List styling for beverages and desserts */
.beverage-list,
.dessert-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.beverage-list li,
.dessert-list li {
  font-size: 1rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e0c4b2;
  display: flex;
  justify-content: space-between;
  color: #5c2e1f;
}

.beverage-list li span,
.dessert-list li span {
  color: #8c4c2f;
  font-weight: bold;
}

h2 {
    text-align: center;
    animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Add staggered animation for menu items */
.menu-item:nth-child(1) { animation: slideInLeft 0.8s ease-out 0.1s both; }
.menu-item:nth-child(2) { animation: slideInLeft 0.8s ease-out 0.3s both; }
.menu-item:nth-child(3) { animation: slideInLeft 0.8s ease-out 0.5s both; }

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Add floating effect */
.menu-item {
  animation: float 6s ease-in-out infinite;
}

.menu-item:nth-child(2) {
  animation-delay: -2s;
}

.menu-item:nth-child(3) {
  animation-delay: -4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

